<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Etiketleme - RFID Envanter <PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        /* RFID Connection Test Styles */
        .connection-test {
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .connection-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }

        .connection-status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            display: block;
        }

        .connection-status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            display: block;
        }

        .connection-status.testing {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            display: block;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #6c757d;
        }

        .status-light.connected {
            background-color: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
            animation: pulse 2s infinite;
        }

        .status-light.disconnected {
            background-color: #dc3545;
        }

        .status-light.testing {
            background-color: #ffc107;
            animation: blink 1s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        #testPrinterConnectionBtn {
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        #testPrinterConnectionBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        #testPrinterConnectionBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .test-success {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            color: white !important;
        }

        .test-error {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="logo">
                <h2>RFID Sistem</h2>
            </div>
            <ul class="nav-links">
                <li>
                    <a href="../index.html">
                        <i class="fas fa-home"></i>
                        <span>Ana Sayfa</span>
                    </a>
                </li>
                <li>
                    <a href="malzemeler.html">
                        <i class="fas fa-boxes"></i>
                        <span>Malzemeler</span>
                    </a>
                </li>
                <li>
                    <a href="sayim.html">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Sayım</span>
                    </a>
                </li>
                <li>
                    <a href="ambar-fisi.html">
                        <i class="fas fa-receipt"></i>
                        <span>Ambar Fişi</span>
                    </a>
                </li>
                <li>
                    <a href="satin-alma.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Satın Alma</span>
                    </a>
                </li>
                <li>
                    <a href="satis.html">
                        <i class="fas fa-cash-register"></i>
                        <span>Satış</span>
                    </a>
                </li>
                <li class="active">
                    <a href="etiketleme.html">
                        <i class="fas fa-tags"></i>
                        <span>Etiketleme</span>
                    </a>
                </li>
                <li>
                    <a href="ayarlar.html">
                        <i class="fas fa-cog"></i>
                        <span>Ayarlar</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <header>
                <div class="header-content">
                    <button class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="search-box">
                        <input type="text" placeholder="Etiket ara...">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="user-info">
                        <span>Hoş Geldiniz, Kullanıcı</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </header>

            <div class="content">
                <div class="page-header">
                    <h1>RFID Etiketleme</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" id="newTagBtn">
                            <i class="fas fa-plus"></i> Yeni Etiket
                        </button>
                        <button class="btn btn-secondary" id="printTagsBtn">
                            <i class="fas fa-print"></i> Etiket Yazdır
                        </button>
                    </div>
                </div>

                <!-- Device Status Cards -->
                <div class="status-cards">
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-print"></i>
                        </div>
                        <div class="status-info">
                            <h3>RFID Yazıcı</h3>
                            <div class="status-indicator">
                                <div class="status-light disconnected" id="printerStatus"></div>
                                <span id="printerStatusText">Bağlı Değil</span>
                            </div>
                        </div>
                        <div class="status-actions">
                            <button class="btn btn-sm btn-secondary" id="connectPrinterBtn">
                                <i class="fas fa-plug"></i> Bağlan
                            </button>
                            <button class="btn btn-sm btn-icon" id="printerSettingsBtn" title="Yazıcı Ayarları">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>

                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-wifi"></i>
                        </div>
                        <div class="status-info">
                            <h3>RFID Okuyucu</h3>
                            <div class="status-indicator">
                                <div class="status-light disconnected" id="readerStatus"></div>
                                <span id="readerStatusText">Bağlı Değil</span>
                            </div>
                        </div>
                        <div class="status-actions">
                            <button class="btn btn-sm btn-secondary" id="connectReaderBtn">
                                <i class="fas fa-plug"></i> Bağlan
                            </button>
                            <button class="btn btn-sm btn-icon" id="readerSettingsBtn" title="Okuyucu Ayarları">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>

                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="status-info">
                            <h3>Toplam Etiket</h3>
                            <div class="status-number" id="totalTags">0</div>
                        </div>
                    </div>

                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="status-info">
                            <h3>Aktif Etiket</h3>
                            <div class="status-number" id="activeTags">0</div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="main-panel">
                    <div class="panel-header">
                        <h2>Etiket Yönetimi</h2>
                        <div class="panel-actions">
                            <button class="btn btn-secondary" id="scanTagsBtn">
                                <i class="fas fa-wifi"></i> Etiketleri Tara
                            </button>
                            <button class="btn btn-secondary" id="refreshTagsBtn">
                                <i class="fas fa-sync"></i> Yenile
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="filters-panel">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="tagTypeFilter">Etiket Tipi:</label>
                                <select id="tagTypeFilter" class="form-control">
                                    <option value="">Tümü</option>
                                    <option value="malzeme">Malzeme</option>
                                    <option value="lokasyon">Lokasyon</option>
                                    <option value="palet">Palet</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="tagStatusFilter">Durum:</label>
                                <select id="tagStatusFilter" class="form-control">
                                    <option value="">Tümü</option>
                                    <option value="aktif">Aktif</option>
                                    <option value="pasif">Pasif</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="dateFilter">Tarih Aralığı:</label>
                                <input type="date" id="dateFromFilter" class="form-control">
                                <span>-</span>
                                <input type="date" id="dateToFilter" class="form-control">
                            </div>
                            <div class="filter-actions">
                                <button class="btn btn-primary" id="filterTagsBtn">
                                    <i class="fas fa-filter"></i> Filtrele
                                </button>
                                <button class="btn btn-secondary" id="clearFiltersBtn">
                                    <i class="fas fa-times"></i> Temizle
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tags Table -->
                    <div class="table-container">
                        <table id="tagsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAllTags"></th>
                                    <th>EPC</th>
                                    <th>Etiket Tipi</th>
                                    <th>Bağlı Öğe</th>
                                    <th>Oluşturma Tarihi</th>
                                    <th>Son Okuma</th>
                                    <th>Durum</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination">
                        <button class="btn-page prev" id="prevPageBtn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="page-numbers" id="pageNumbers">
                            <!-- Page numbers will be generated here -->
                        </div>
                        <button class="btn-page next" id="nextPageBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- New Tag Modal -->
    <div class="modal" id="tagModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Yeni Etiket Oluştur</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="tagForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="tagType">Etiket Tipi *</label>
                            <select id="tagType" class="form-control" required>
                                <option value="">Seçiniz</option>
                                <option value="malzeme">Malzeme</option>
                                <option value="lokasyon">Lokasyon</option>
                                <option value="palet">Palet</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tagTemplate">Etiket Şablonu *</label>
                            <select id="tagTemplate" class="form-control" required>
                                <option value="">Seçiniz</option>
                                <option value="template1">Standart Etiket</option>
                                <option value="template2">QR Kodlu Etiket</option>
                                <option value="template3">Büyük Etiket</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="tagEPC">EPC Kodu</label>
                        <div class="input-with-button">
                            <input type="text" id="tagEPC" class="form-control" placeholder="Otomatik oluşturulacak">
                            <button type="button" class="btn btn-secondary" id="generateEPCBtn" title="EPC Oluştur">
                                <i class="fas fa-random"></i>
                            </button>
                            <button type="button" class="btn btn-secondary" id="readEPCBtn" title="RFID'den Oku">
                                <i class="fas fa-wifi"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group" id="itemSelectGroup">
                        <label for="tagItem">Bağlanacak Öğe *</label>
                        <div class="input-with-button">
                            <input type="text" id="tagItem" class="form-control" placeholder="Malzeme kodu veya adı" required>
                            <button type="button" class="btn btn-secondary" id="searchItemBtn" title="Öğe Ara">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="tagDescription">Açıklama</label>
                        <textarea id="tagDescription" class="form-control" rows="2" placeholder="İsteğe bağlı açıklama"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Durum</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="tagStatus" class="toggle-input" checked>
                                <label for="tagStatus" class="toggle-label"></label>
                                <span class="toggle-text">Aktif</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Yazdırma</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="printAfterSave" class="toggle-input" checked>
                                <label for="printAfterSave" class="toggle-label"></label>
                                <span class="toggle-text">Kaydettikten sonra yazdır</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelTagBtn">İptal</button>
                <button class="btn btn-primary" id="saveTagBtn">
                    <i class="fas fa-save"></i> Kaydet
                </button>
            </div>
        </div>
    </div>

    <!-- Print Tags Modal -->
    <div class="modal" id="printTagsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Etiket Yazdır</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="printTagsForm">
                    <div class="form-group">
                        <label for="printTagItems">Yazdırılacak Etiketler *</label>
                        <select id="printTagItems" class="form-control" multiple size="6" required>
                            <!-- Options will be loaded dynamically -->
                        </select>
                        <small class="form-text">Ctrl tuşu ile birden fazla etiket seçebilirsiniz</small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="printTagCopies">Kopya Sayısı</label>
                            <input type="number" id="printTagCopies" class="form-control" min="1" max="10" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="printTagPrinter">Yazıcı</label>
                            <select id="printTagPrinter" class="form-control" required>
                                <option value="default">Varsayılan RFID Yazıcı</option>
                                <option value="zebra">Zebra ZT411</option>
                                <option value="sato">SATO CL4NX</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelPrintTagsBtn">İptal</button>
                <button class="btn btn-primary" id="confirmPrintTagsBtn">
                    <i class="fas fa-print"></i> Yazdır
                </button>
            </div>
        </div>
    </div>

    <!-- Printer Settings Modal -->
    <div class="modal" id="printerSettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>RFID Yazıcı Ayarları</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="printerSettingsForm">
                    <div class="form-group">
                        <label for="printerIP">IP Adresi *</label>
                        <input type="text" id="printerIP" class="form-control" value="*************" required
                               pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$"
                               placeholder="*************">
                        <small class="form-text">RFID yazıcının IP adresini girin (örn: *************)</small>
                    </div>

                    <div class="form-group">
                        <label for="printerPort">Port *</label>
                        <input type="number" id="printerPort" class="form-control" value="9100" required
                               min="1" max="65535" placeholder="9100">
                        <small class="form-text">Yazıcının port numarasını girin</small>
                    </div>

                    <div class="form-group">
                        <label for="printerTimeout">Bağlantı Zaman Aşımı (saniye)</label>
                        <input type="number" id="printerTimeout" class="form-control" value="5"
                               min="1" max="30" placeholder="5">
                    </div>

                    <div class="connection-test">
                        <button type="button" class="btn btn-secondary" id="testPrinterConnectionBtn">
                            <i class="fas fa-plug"></i> Bağlantıyı Test Et
                        </button>
                        <div class="connection-status" id="printerConnectionStatus"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelPrinterSettingsBtn">İptal</button>
                <button class="btn btn-primary" id="savePrinterSettingsBtn">
                    <i class="fas fa-save"></i> Kaydet
                </button>
            </div>
        </div>
    </div>

    <!-- Reader Settings Modal -->
    <div class="modal" id="readerSettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>RFID Okuyucu Ayarları</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="readerSettingsForm">
                    <div class="form-group">
                        <label for="readerIP">IP Adresi *</label>
                        <input type="text" id="readerIP" class="form-control" value="*************" required
                               pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$"
                               placeholder="*************">
                        <small class="form-text">Okuyucunun IP adresini girin</small>
                    </div>

                    <div class="form-group">
                        <label for="readerPort">Port *</label>
                        <input type="number" id="readerPort" class="form-control" value="8080" required
                               min="1" max="65535" placeholder="8080">
                        <small class="form-text">Okuyucunun port numarasını girin</small>
                    </div>

                    <div class="form-group">
                        <label for="readerTimeout">Bağlantı Zaman Aşımı (saniye)</label>
                        <input type="number" id="readerTimeout" class="form-control" value="5"
                               min="1" max="30" placeholder="5">
                    </div>

                    <div class="connection-test">
                        <button type="button" class="btn btn-secondary" id="testReaderConnectionBtn">
                            <i class="fas fa-plug"></i> Bağlantıyı Test Et
                        </button>
                        <div class="connection-status" id="readerConnectionStatus"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelReaderSettingsBtn">İptal</button>
                <button class="btn btn-primary" id="saveReaderSettingsBtn">
                    <i class="fas fa-save"></i> Kaydet
                </button>
            </div>
        </div>
    </div>

    <!-- Paket Göç Modal -->
    <div class="modal" id="paketGocModal">
        <div class="modal-content paket-goc-modal">
            <div class="modal-header">
                <h3>Paket Göç</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="paket-goc-form">
                    <!-- Üst satır -->
                    <div class="form-row-3">
                        <div class="form-group">
                            <label class="form-label-brown">Önüm</label>
                            <select id="onum-select" class="form-control">
                                <option value="">Yükleniyor...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label-brown">Varyant</label>
                            <select id="varyant-select" class="form-control varyant-select">
                                <option value="">Önce ürün seçiniz</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label-brown">Rezin renk</label>
                            <select id="rezin-select" class="form-control">
                                <option value="">Önce ürün seçiniz</option>
                            </select>
                        </div>
                    </div>

                    <!-- Alt satır -->
                    <div class="form-row-3">
                        <div class="form-group">
                            <label class="form-label-brown">EPC Kodu</label>
                            <input type="text" id="epc-input" class="form-control" placeholder="24 karakter EPC kodu girin..." maxlength="24">
                        </div>
                        <div class="form-group">
                            <label class="form-label-brown">Çalışık</label>
                            <select id="calisik-select" class="form-control">
                                <option value="">Yükleniyor...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label-brown">COM PORT</label>
                            <select id="comport-select" class="form-control">
                                <option value="">Taranıyor...</option>
                            </select>
                        </div>
                    </div>

                    <!-- Bilgi paneli -->
                    <div class="info-panel">
                        <div class="info-item">
                            <span class="info-label">PLANLANAN:</span>
                            <span class="info-value" id="planlananValue">label1</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">ÇIKARYLDY:</span>
                            <span class="info-value" id="cikaryldyValue">label2</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">GALDY:</span>
                            <span class="info-value" id="galdyValue">label0</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer paket-goc-footer">
                <button class="btn btn-icon" id="teraziBtn" title="Terazi">
                    <i class="fas fa-balance-scale"></i>
                </button>
                <button class="btn btn-icon" id="yazdirBtn" title="Yazdır">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-success btn-gos" id="gosBtn">
                    GÖŞ
                </button>
            </div>
        </div>
    </div>

    <!-- Terazi Ayarları Modal -->
    <div class="modal" id="teraziAyarlariModal">
        <div class="modal-content terazi-modal">
            <div class="modal-header">
                <h3>Terazi Ayarları</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="terazi-container">
                    <!-- Sol Panel - Com Port Control -->
                    <div class="terazi-panel">
                        <h4 class="panel-title">Com Port Control</h4>

                        <div class="terazi-form-group">
                            <label for="baudRate">BAUD RATE</label>
                            <select id="baudRate" class="terazi-select">
                                <option value="9600" selected>9600</option>
                                <option value="4800">4800</option>
                                <option value="19200">19200</option>
                                <option value="38400">38400</option>
                                <option value="57600">57600</option>
                                <option value="115200">115200</option>
                            </select>
                        </div>

                        <div class="terazi-form-group">
                            <label for="dataBits">DATA BITS</label>
                            <select id="dataBits" class="terazi-select">
                                <option value="8" selected>8</option>
                                <option value="7">7</option>
                                <option value="6">6</option>
                                <option value="5">5</option>
                            </select>
                        </div>

                        <div class="terazi-form-group">
                            <label for="stopBits">STOP BITS</label>
                            <select id="stopBits" class="terazi-select">
                                <option value="One" selected>One</option>
                                <option value="Two">Two</option>
                                <option value="OnePointFive">OnePointFive</option>
                            </select>
                        </div>

                        <div class="terazi-form-group">
                            <label for="parityBits">PARITY BITS</label>
                            <select id="parityBits" class="terazi-select">
                                <option value="None" selected>None</option>
                                <option value="Even">Even</option>
                                <option value="Odd">Odd</option>
                                <option value="Mark">Mark</option>
                                <option value="Space">Space</option>
                            </select>
                        </div>

                        <!-- Port Control Buttons -->
                        <div class="port-control">
                            <div class="port-buttons">
                                <button class="btn btn-success" id="openPortBtn">OPEN</button>
                                <button class="btn btn-danger" id="closePortBtn">CLOSE</button>
                            </div>

                            <div class="port-status">
                                <span class="status-label">COM PORT STATUS</span>
                                <div class="status-indicator">
                                    <span class="status-text" id="portStatusText">OFF</span>
                                </div>
                            </div>
                        </div>

                        <!-- Port Selection -->
                        <div class="port-selection">
                            <select id="portName" class="terazi-select">
                                <option value="">COM Port seçiniz</option>
                            </select>
                        </div>
                    </div>

                    <!-- Sağ Panel - Receiver Control -->
                    <div class="terazi-panel">
                        <h4 class="panel-title">Receiver Control</h4>

                        <div class="receiver-area">
                            <textarea id="receiverData" class="receiver-textarea" readonly placeholder="Terazi verisi burada görünecek..."></textarea>
                        </div>

                        <div class="receiver-controls">
                            <div class="receiver-buttons">
                                <button class="btn btn-secondary" id="clearDataBtn">Clear Data IN</button>
                            </div>

                            <div class="receiver-options">
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="alwaysUpdate">
                                        <span class="checkmark"></span>
                                        Always Update
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="addToOldData" checked>
                                        <span class="checkmark"></span>
                                        Add To Old Data
                                    </label>
                                </div>
                            </div>

                            <div class="data-length">
                                <span class="data-label">Data IN Length:</span>
                                <span class="data-value" id="dataLength">00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelTeraziBtn">İptal</button>
                <button class="btn btn-primary" id="saveTeraziBtn">
                    <i class="fas fa-save"></i> Kaydet
                </button>
            </div>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script src="../js/pages/etiketleme.js"></script>
</body>
</html>
