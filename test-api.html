<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>API Test Sayfası</h1>
    
    <div>
        <h2>Firma Numaraları</h2>
        <button onclick="testFirmNumbers()">Test Firma Numbers API</button>
        <div id="firmResult"></div>
    </div>
    
    <div>
        <h2>Malzemeler</h2>
        <button onclick="testMaterials()">Test Materials API</button>
        <div id="materialsResult"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000';

        function testFirmNumbers() {
            console.log('Testing firm numbers API...');
            document.getElementById('firmResult').innerHTML = 'Yükleniyor...';
            
            fetch(`${API_BASE_URL}/api/firm-numbers`)
                .then(response => {
                    console.log('Firm numbers response:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Firm numbers data:', data);
                    document.getElementById('firmResult').innerHTML = 
                        `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('firmResult').innerHTML = 
                        `<p style="color: red;">Hata: ${error.message}</p>`;
                });
        }

        function testMaterials() {
            console.log('Testing materials API...');
            document.getElementById('materialsResult').innerHTML = 'Yükleniyor...';
            
            fetch(`${API_BASE_URL}/api/all-materials`)
                .then(response => {
                    console.log('Materials response:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Materials data length:', data.data ? data.data.length : 'No data');
                    document.getElementById('materialsResult').innerHTML = 
                        `<p>Başarılı! ${data.data ? data.data.length : 0} malzeme yüklendi.</p>
                         <p>İlk malzeme: <pre>${JSON.stringify(data.data ? data.data[0] : 'Veri yok', null, 2)}</pre></p>`;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('materialsResult').innerHTML = 
                        `<p style="color: red;">Hata: ${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
