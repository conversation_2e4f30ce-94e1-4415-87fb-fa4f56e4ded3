document.addEventListener('DOMContentLoaded', function() {
    // Device connection settings
    let printerSettings = {
        ip: '*************',
        port: 9100,
        timeout: 5,
        connected: false
    };

    let readerSettings = {
        ip: '*************',
        port: 8080,
        timeout: 5,
        connected: false
    };

    // Load settings from localStorage
    const savedPrinterSettings = localStorage.getItem('printerSettings');
    const savedReaderSettings = localStorage.getItem('readerSettings');

    if (savedPrinterSettings) {
        printerSettings = { ...printerSettings, ...JSON.parse(savedPrinterSettings) };
    }

    if (savedReaderSettings) {
        readerSettings = { ...readerSettings, ...JSON.parse(savedReaderSettings) };
    }

    // DOM elements
    const connectPrinterBtn = document.getElementById('connectPrinterBtn');
    const connectReaderBtn = document.getElementById('connectReaderBtn');
    const printerSettingsBtn = document.getElementById('printerSettingsBtn');
    const readerSettingsBtn = document.getElementById('readerSettingsBtn');

    // Debug: Check if elements exist
    console.log('connectPrinterBtn:', connectPrinterBtn);
    console.log('connectReaderBtn:', connectReaderBtn);
    console.log('printerSettingsBtn:', printerSettingsBtn);
    console.log('readerSettingsBtn:', readerSettingsBtn);

    // Printer connection
    if (connectPrinterBtn) {
        connectPrinterBtn.addEventListener('click', function() {
            if (printerSettings.connected) {
                disconnectPrinter();
            } else {
                connectToPrinter();
            }
        });
    }

    async function connectToPrinter() {
        connectPrinterBtn.disabled = true;
        connectPrinterBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Bağlanıyor...';

        try {
            // Test real connection
            const success = await testConnection(printerSettings.ip, printerSettings.port);

            if (success) {
                printerSettings.connected = true;
                updatePrinterStatus(true, `Bağlı: ${printerSettings.ip}:${printerSettings.port}`);
                connectPrinterBtn.innerHTML = '<i class="fas fa-unlink"></i> Bağlantıyı Kes';
                connectPrinterBtn.classList.remove('btn-secondary');
                connectPrinterBtn.classList.add('connected');
                showNotification('RFID yazıcı bağlantısı kuruldu.', 'success');
            } else {
                printerSettings.connected = false;
                updatePrinterStatus(false, 'Bağlantı Hatası');
                connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
                showNotification(`RFID yazıcı bağlantısı kurulamadı: ${printerSettings.ip}:${printerSettings.port}`, 'error');
            }
        } catch (error) {
            printerSettings.connected = false;
            updatePrinterStatus(false, 'Bağlantı Hatası');
            connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
            showNotification(`Bağlantı hatası: ${error.message}`, 'error');
        }

        connectPrinterBtn.disabled = false;
    }

    function disconnectPrinter() {
        printerSettings.connected = false;
        updatePrinterStatus(false, 'Bağlı Değil');
        connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
        connectPrinterBtn.classList.remove('connected');
        connectPrinterBtn.classList.add('btn-secondary');
        showNotification('RFID yazıcı bağlantısı kesildi.', 'info');
    }

    // Reader connection
    if (connectReaderBtn) {
        connectReaderBtn.addEventListener('click', function() {
            if (readerSettings.connected) {
                disconnectReader();
            } else {
                connectToReader();
            }
        });
    }

    async function connectToReader() {
        connectReaderBtn.disabled = true;
        connectReaderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Bağlanıyor...';

        try {
            // Test real connection
            const success = await testConnection(readerSettings.ip, readerSettings.port);

            if (success) {
                readerSettings.connected = true;
                updateReaderStatus(true, `Bağlı: ${readerSettings.ip}:${readerSettings.port}`);
                connectReaderBtn.innerHTML = '<i class="fas fa-unlink"></i> Bağlantıyı Kes';
                connectReaderBtn.classList.remove('btn-secondary');
                connectReaderBtn.classList.add('connected');
                showNotification('RFID okuyucu bağlantısı kuruldu.', 'success');
            } else {
                readerSettings.connected = false;
                updateReaderStatus(false, 'Bağlantı Hatası');
                connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
                showNotification(`RFID okuyucu bağlantısı kurulamadı: ${readerSettings.ip}:${readerSettings.port}`, 'error');
            }
        } catch (error) {
            readerSettings.connected = false;
            updateReaderStatus(false, 'Bağlantı Hatası');
            connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
            showNotification(`Bağlantı hatası: ${error.message}`, 'error');
        }

        connectReaderBtn.disabled = false;
    }

    function disconnectReader() {
        readerSettings.connected = false;
        updateReaderStatus(false, 'Bağlı Değil');
        connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
        connectReaderBtn.classList.remove('connected');
        connectReaderBtn.classList.add('btn-secondary');
        showNotification('RFID okuyucu bağlantısı kesildi.', 'info');
    }

    // Status update functions
    function updatePrinterStatus(connected, statusText) {
        const statusLight = document.getElementById('printerStatus');
        const statusTextElement = document.getElementById('printerStatusText');

        if (connected) {
            statusLight.classList.remove('disconnected');
            statusLight.classList.add('connected');
        } else {
            statusLight.classList.remove('connected');
            statusLight.classList.add('disconnected');
        }

        statusTextElement.textContent = statusText;
    }

    function updateReaderStatus(connected, statusText) {
        const statusLight = document.getElementById('readerStatus');
        const statusTextElement = document.getElementById('readerStatusText');

        if (connected) {
            statusLight.classList.remove('disconnected');
            statusLight.classList.add('connected');
        } else {
            statusLight.classList.remove('connected');
            statusLight.classList.add('disconnected');
        }

        statusTextElement.textContent = statusText;
    }

    // Enhanced RFID Printer Connection Test Function
    async function testRFIDPrinterConnection(ip, port, timeout = 5000) {
        return new Promise(async (resolve) => {
            console.log(`Testing RFID printer connection to ${ip}:${port}`);

            // Validate IP format first
            const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
            if (!ipRegex.test(ip)) {
                console.log('Invalid IP format');
                resolve(false);
                return;
            }

            // Test methods for RFID printers
            const testMethods = [
                // Method 1: Try to send a simple ZPL status command
                async () => {
                    try {
                        console.log('Testing Method 1: ZPL Status Command via WebSocket simulation');
                        // Simulate ZPL printer status check
                        const response = await fetch(`http://${ip}:${port}`, {
                            method: 'GET',
                            mode: 'no-cors',
                            signal: AbortSignal.timeout(timeout)
                        });
                        return true; // If no error thrown, connection exists
                    } catch (error) {
                        console.log('Method 1 failed:', error.message);
                        return false;
                    }
                },

                // Method 2: Try HTTP connection (some RFID printers have web interface)
                async () => {
                    try {
                        console.log('Testing Method 2: HTTP Connection');
                        const response = await fetch(`http://${ip}:${port}`, {
                            method: 'GET',
                            mode: 'no-cors',
                            signal: AbortSignal.timeout(timeout)
                        });
                        return true;
                    } catch (error) {
                        console.log('Method 2 failed:', error.message);
                        return false;
                    }
                },

                // Method 3: Try HTTPS connection
                async () => {
                    try {
                        console.log('Testing Method 3: HTTPS Connection');
                        const response = await fetch(`https://${ip}:${port}`, {
                            method: 'GET',
                            mode: 'no-cors',
                            signal: AbortSignal.timeout(timeout)
                        });
                        return true;
                    } catch (error) {
                        console.log('Method 3 failed:', error.message);
                        return false;
                    }
                },

                // Method 4: Try backend API test if available
                async () => {
                    try {
                        console.log('Testing Method 4: Backend API Test');
                        const response = await fetch(`/api/rfid/test-printer-connection?ip=${ip}&port=${port}&timeout=${timeout/1000}`, {
                            method: 'GET',
                            signal: AbortSignal.timeout(timeout)
                        });

                        if (response.ok) {
                            const data = await response.json();
                            return data.success;
                        }
                        return false;
                    } catch (error) {
                        console.log('Method 4 failed:', error.message);
                        return false;
                    }
                },

                // Method 5: Ping-like test using image loading
                async () => {
                    try {
                        console.log('Testing Method 5: Ping-like test');
                        return new Promise((pingResolve) => {
                            const img = new Image();
                            const timeoutId = setTimeout(() => {
                                pingResolve(false);
                            }, timeout);

                            img.onload = () => {
                                clearTimeout(timeoutId);
                                pingResolve(true);
                            };

                            img.onerror = () => {
                                clearTimeout(timeoutId);
                                // Even on error, if we get a response, the device is reachable
                                pingResolve(true);
                            };

                            img.src = `http://${ip}:${port}/favicon.ico?t=${Date.now()}`;
                        });
                    } catch (error) {
                        console.log('Method 5 failed:', error.message);
                        return false;
                    }
                }
            ];

            // Try each method sequentially
            for (let i = 0; i < testMethods.length; i++) {
                try {
                    console.log(`Trying test method ${i + 1}...`);
                    const result = await testMethods[i]();
                    if (result) {
                        console.log(`Test method ${i + 1} succeeded!`);
                        resolve(true);
                        return;
                    }
                } catch (error) {
                    console.log(`Test method ${i + 1} failed:`, error.message);
                    continue;
                }
            }

            // If all methods failed
            console.log('All test methods failed');
            resolve(false);
        });
    }

    // Legacy testConnection function - updated to use new RFID test
    async function testConnection(ip, port, timeout = 5000) {
        return await testRFIDPrinterConnection(ip, port, timeout);
    }

    // Settings modal functionality
    const printerSettingsModal = document.getElementById('printerSettingsModal');
    const readerSettingsModal = document.getElementById('readerSettingsModal');

    // Printer settings
    if (printerSettingsBtn) {
        printerSettingsBtn.addEventListener('click', function() {
            openPrinterSettings();
        });
    }

    function openPrinterSettings() {
        // Populate form with current settings
        document.getElementById('printerIP').value = printerSettings.ip;
        document.getElementById('printerPort').value = printerSettings.port;
        document.getElementById('printerTimeout').value = printerSettings.timeout;

        // Clear connection status
        const connectionStatus = document.getElementById('printerConnectionStatus');
        connectionStatus.style.display = 'none';
        connectionStatus.className = 'connection-status';

        printerSettingsModal.style.display = 'flex';
    }

    // Test Printer Connection Button Event Listener
    const testPrinterConnectionBtn = document.getElementById('testPrinterConnectionBtn');
    if (testPrinterConnectionBtn) {
        testPrinterConnectionBtn.addEventListener('click', async function() {
            await testPrinterConnection();
        });
    }

    // Test Printer Connection Function
    async function testPrinterConnection() {
        const testBtn = document.getElementById('testPrinterConnectionBtn');
        const connectionStatus = document.getElementById('printerConnectionStatus');

        // Get form values
        const ip = document.getElementById('printerIP').value.trim();
        const port = document.getElementById('printerPort').value.trim();
        const timeout = document.getElementById('printerTimeout').value.trim();

        // Validate inputs
        if (!ip) {
            showConnectionStatus('error', 'IP adresi gerekli!');
            return;
        }

        if (!port) {
            showConnectionStatus('error', 'Port numarası gerekli!');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        if (!ipRegex.test(ip)) {
            showConnectionStatus('error', 'Geçersiz IP adresi formatı!');
            return;
        }

        // Validate port range
        const portNum = parseInt(port);
        if (portNum < 1 || portNum > 65535) {
            showConnectionStatus('error', 'Port numarası 1-65535 arasında olmalı!');
            return;
        }

        // Show testing state
        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test Ediliyor...';
        testBtn.classList.remove('test-success', 'test-error');

        showConnectionStatus('testing', 'RFID yazıcı bağlantısı test ediliyor...');

        try {
            // Test connection using multiple methods
            const success = await testRFIDPrinterConnection(ip, portNum, parseInt(timeout) * 1000);

            if (success) {
                // Success state
                testBtn.innerHTML = '<i class="fas fa-check"></i> Bağlantı Başarılı!';
                testBtn.classList.add('test-success');
                showConnectionStatus('success', `✅ RFID yazıcı bağlantısı başarılı! (${ip}:${port})`);

                // Update printer settings
                printerSettings.ip = ip;
                printerSettings.port = portNum;
                printerSettings.timeout = parseInt(timeout);
                printerSettings.connected = true;

                // Save to localStorage
                localStorage.setItem('printerSettings', JSON.stringify(printerSettings));

                showNotification('RFID yazıcı bağlantısı başarılı!', 'success');
            } else {
                // Error state
                testBtn.innerHTML = '<i class="fas fa-times"></i> Bağlantı Başarısız!';
                testBtn.classList.add('test-error');
                showConnectionStatus('error', `❌ RFID yazıcı bağlantısı başarısız! (${ip}:${port})`);
                showNotification('RFID yazıcı bağlantısı kurulamadı!', 'error');
            }
        } catch (error) {
            // Error state
            testBtn.innerHTML = '<i class="fas fa-times"></i> Bağlantı Hatası!';
            testBtn.classList.add('test-error');
            showConnectionStatus('error', `❌ Bağlantı hatası: ${error.message}`);
            showNotification(`Bağlantı hatası: ${error.message}`, 'error');
        }

        // Reset button after 3 seconds
        setTimeout(() => {
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlantıyı Test Et';
            testBtn.classList.remove('test-success', 'test-error');
        }, 3000);
    }

    // Show Connection Status Helper Function
    function showConnectionStatus(type, message) {
        const connectionStatus = document.getElementById('printerConnectionStatus');

        // Clear previous classes
        connectionStatus.className = 'connection-status';

        // Add new class and show
        connectionStatus.classList.add(type);
        connectionStatus.style.display = 'block';

        // Create status content
        const statusLight = type === 'success' ? 'connected' :
                           type === 'testing' ? 'testing' : 'disconnected';

        connectionStatus.innerHTML = `
            <div class="status-indicator">
                <div class="status-light ${statusLight}"></div>
                <span>${message}</span>
            </div>
        `;
    }

    // Reader settings
    if (readerSettingsBtn) {
        readerSettingsBtn.addEventListener('click', function() {
            openReaderSettings();
        });
    }

    function openReaderSettings() {
        // Populate form with current settings
        document.getElementById('readerIP').value = readerSettings.ip;
        document.getElementById('readerPort').value = readerSettings.port;
        document.getElementById('readerTimeout').value = readerSettings.timeout;

        // Clear connection status
        document.getElementById('readerConnectionStatus').style.display = 'none';

        readerSettingsModal.style.display = 'flex';
    }

    // Test connections
    const testPrinterBtn = document.getElementById('testPrinterConnectionBtn');
    const testReaderBtn = document.getElementById('testReaderConnectionBtn');

    if (testPrinterBtn) {
        testPrinterBtn.addEventListener('click', function() {
            testPrinterConnection();
        });
    }

    if (testReaderBtn) {
        testReaderBtn.addEventListener('click', function() {
            testReaderConnection();
        });
    }

    async function testPrinterConnection() {
        const ip = document.getElementById('printerIP').value;
        const port = document.getElementById('printerPort').value;
        const statusDiv = document.getElementById('printerConnectionStatus');
        const testBtn = document.getElementById('testPrinterConnectionBtn');

        if (!ip || !port) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = 'IP adresi ve port gerekli!';
            return;
        }

        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test ediliyor...';
        statusDiv.className = 'connection-status testing';
        statusDiv.textContent = `${ip}:${port} adresine bağlanılıyor...`;

        try {
            const success = await testConnection(ip, port, 5000);

            if (success) {
                statusDiv.className = 'connection-status success';
                statusDiv.textContent = `✓ Bağlantı başarılı! (${ip}:${port})`;
            } else {
                statusDiv.className = 'connection-status error';
                statusDiv.textContent = `✗ Bağlantı başarısız! (${ip}:${port})`;
            }
        } catch (error) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = `✗ Bağlantı hatası: ${error.message}`;
        }

        testBtn.disabled = false;
        testBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlantıyı Test Et';
    }

    async function testReaderConnection() {
        const ip = document.getElementById('readerIP').value;
        const port = document.getElementById('readerPort').value;
        const statusDiv = document.getElementById('readerConnectionStatus');
        const testBtn = document.getElementById('testReaderConnectionBtn');

        if (!ip || !port) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = 'IP adresi ve port gerekli!';
            return;
        }

        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test ediliyor...';
        statusDiv.className = 'connection-status testing';
        statusDiv.textContent = `${ip}:${port} adresine bağlanılıyor...`;

        try {
            const success = await testConnection(ip, port, 5000);

            if (success) {
                statusDiv.className = 'connection-status success';
                statusDiv.textContent = `✓ Bağlantı başarılı! (${ip}:${port})`;
            } else {
                statusDiv.className = 'connection-status error';
                statusDiv.textContent = `✗ Bağlantı başarısız! (${ip}:${port})`;
            }
        } catch (error) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = `✗ Bağlantı hatası: ${error.message}`;
        }

        testBtn.disabled = false;
        testBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlantıyı Test Et';
    }

    // Save settings
    const savePrinterBtn = document.getElementById('savePrinterSettingsBtn');
    const saveReaderBtn = document.getElementById('saveReaderSettingsBtn');

    if (savePrinterBtn) {
        savePrinterBtn.addEventListener('click', function() {
            savePrinterSettings();
        });
    }

    if (saveReaderBtn) {
        saveReaderBtn.addEventListener('click', function() {
            saveReaderSettings();
        });
    }

    function savePrinterSettings() {
        const ip = document.getElementById('printerIP').value;
        const port = parseInt(document.getElementById('printerPort').value);
        const timeout = parseInt(document.getElementById('printerTimeout').value);

        if (!ip || !port || !timeout) {
            showNotification('Lütfen tüm alanları doldurun!', 'error');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        if (!ipRegex.test(ip)) {
            showNotification('Geçersiz IP adresi formatı!', 'error');
            return;
        }

        // Update settings
        printerSettings.ip = ip;
        printerSettings.port = port;
        printerSettings.timeout = timeout;

        // Save to localStorage
        localStorage.setItem('printerSettings', JSON.stringify(printerSettings));

        // Close modal
        printerSettingsModal.style.display = 'none';

        // Disconnect if connected (settings changed)
        if (printerSettings.connected) {
            disconnectPrinter();
        }

        showNotification('Yazıcı ayarları kaydedildi!', 'success');
    }

    function saveReaderSettings() {
        const ip = document.getElementById('readerIP').value;
        const port = parseInt(document.getElementById('readerPort').value);
        const timeout = parseInt(document.getElementById('readerTimeout').value);

        if (!ip || !port || !timeout) {
            showNotification('Lütfen tüm alanları doldurun!', 'error');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        if (!ipRegex.test(ip)) {
            showNotification('Geçersiz IP adresi formatı!', 'error');
            return;
        }

        // Update settings
        readerSettings.ip = ip;
        readerSettings.port = port;
        readerSettings.timeout = timeout;

        // Save to localStorage
        localStorage.setItem('readerSettings', JSON.stringify(readerSettings));

        // Close modal
        readerSettingsModal.style.display = 'none';

        // Disconnect if connected (settings changed)
        if (readerSettings.connected) {
            disconnectReader();
        }

        showNotification('Okuyucu ayarları kaydedildi!', 'success');
    }

    // Modal close functionality
    document.querySelectorAll('.close-modal').forEach(closeBtn => {
        closeBtn.addEventListener('click', async function() {
            const modal = this.closest('.modal');
            // Stop data reading if terazi modal is being closed
            if (modal && modal.id === 'teraziAyarlariModal') {
                stopDataReading();
            }
            // Close agramy COM port if paket goc modal is being closed
            else if (modal && modal.id === 'paketGocModal') {
                if (agramyPortSettings.isConnected && agramyPortSettings.portName) {
                    await closeComPortForWeight(agramyPortSettings.portName);
                }
            }
            modal.style.display = 'none';
        });
    });

    const cancelPrinterBtn = document.getElementById('cancelPrinterSettingsBtn');
    const cancelReaderBtn = document.getElementById('cancelReaderSettingsBtn');

    if (cancelPrinterBtn) {
        cancelPrinterBtn.addEventListener('click', function() {
            printerSettingsModal.style.display = 'none';
        });
    }

    if (cancelReaderBtn) {
        cancelReaderBtn.addEventListener('click', function() {
            readerSettingsModal.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', async function(event) {
        if (event.target.classList.contains('modal')) {
            // Stop data reading if terazi modal is being closed
            if (event.target.id === 'teraziAyarlariModal') {
                stopDataReading();
            }
            // Close agramy COM port if paket goc modal is being closed
            else if (event.target.id === 'paketGocModal') {
                if (agramyPortSettings.isConnected && agramyPortSettings.portName) {
                    await closeComPortForWeight(agramyPortSettings.portName);
                }
            }
            event.target.style.display = 'none';
        }
    });

    // Paket Göç Modal functionality
    const newTagBtn = document.getElementById('newTagBtn');
    const paketGocModal = document.getElementById('paketGocModal');

    if (newTagBtn) {
        newTagBtn.addEventListener('click', function() {
            openPaketGocModal();
        });
    }

    function openPaketGocModal() {
        paketGocModal.style.display = 'flex';
        // Initialize default values
        updatePaketGocInfo();
        // Load dropdown data
        loadDropdownData();
    }

    function updatePaketGocInfo() {
        // Bu fonksiyon gerçek verilerle güncellenecek
        document.getElementById('planlananValue').textContent = 'label1';
        document.getElementById('cikaryldyValue').textContent = 'label2';
        document.getElementById('galdyValue').textContent = 'label0';
    }

    async function loadDropdownData() {
        try {
            showNotification('Dropdown verileri yükleniyor...', 'info');

            // Paralel olarak ürün, çalışık ve COM port verilerini yükle
            const [itemsResponse, shiftsResponse, comPortsResponse] = await Promise.all([
                fetch('http://localhost:5000/api/wms/items'),
                fetch('http://localhost:5000/api/wms/shifts'),
                fetch('http://localhost:5000/api/system/com-ports')
            ]);

            const itemsData = await itemsResponse.json();
            const shiftsData = await shiftsResponse.json();
            const comPortsData = await comPortsResponse.json();

            let successCount = 0;
            let totalCount = 0;

            // Önüm dropdown'ını doldur
            if (itemsData.success) {
                populateDropdown('onum-select', itemsData.data, 'Ürün seçiniz');
                successCount++;
                totalCount += itemsData.count;
            }

            // Çalışık dropdown'ını doldur
            if (shiftsData.success) {
                populateDropdown('calisik-select', shiftsData.data, 'Çalışık seçiniz');
                successCount++;
                totalCount += shiftsData.count;
            }

            // COM Port dropdown'ını doldur
            if (comPortsData.success) {
                populateDropdown('comport-select', comPortsData.data, 'COM Port seçiniz');
                successCount++;
                totalCount += comPortsData.count;

                // Detaylı COM port bilgilerini console'a yazdır
                console.log('Available COM Ports:', comPortsData.detailed_data);
            }

            // Önüm seçimi değiştiğinde varyant ve renk dropdown'larını güncelle
            setupCascadeDropdown();

            // Ağramy input alanı için focus/blur event'lerini ekle
            setupAgramyInput();

            // Terazi buton durumunu başlangıçta güncelle
            updateTeraziButtonStatus(false);

            if (successCount > 0) {
                showNotification(`${totalCount} veri yüklendi (${successCount}/3 başarılı)`, 'success');
            } else {
                showNotification('Dropdown verileri yüklenemedi', 'error');
            }
        } catch (error) {
            console.error('Error loading dropdown data:', error);
            showNotification('Dropdown verileri yüklenirken hata oluştu', 'error');
        }
    }

    function setupCascadeDropdown() {
        const onumSelect = document.getElementById('onum-select');
        const varyantSelect = document.getElementById('varyant-select');
        const rezinSelect = document.getElementById('rezin-select');

        if (onumSelect) {
            onumSelect.addEventListener('change', async function() {
                const selectedItem = this.value;

                if (selectedItem) {
                    // Varyant ve rezin renk dropdown'larını paralel olarak yükle
                    await Promise.all([
                        loadVariants(selectedItem),
                        loadColors(selectedItem)
                    ]);
                } else {
                    // Ürün seçimi temizlendiğinde diğer dropdown'ları temizle
                    if (varyantSelect) {
                        varyantSelect.innerHTML = '<option value="">Önce ürün seçiniz</option>';
                    }
                    if (rezinSelect) {
                        rezinSelect.innerHTML = '<option value="">Önce ürün seçiniz</option>';
                    }
                }
            });
        }
    }

    async function loadVariants(itemName) {
        try {
            const varyantSelect = document.getElementById('varyant-select');

            // Loading state
            varyantSelect.innerHTML = '<option value="">Yükleniyor...</option>';
            varyantSelect.disabled = true;

            // API'den varyantları çek
            const response = await fetch(`http://localhost:5000/api/wms/variants?item=${encodeURIComponent(itemName)}`);
            const data = await response.json();

            if (data.success) {
                if (data.data && data.data.length > 0) {
                    populateDropdown('varyant-select', data.data, 'Varyant seçiniz');
                    showNotification(`${data.count} varyant yüklendi`, 'success');
                } else {
                    // Varyant bulunamadı ama hata değil
                    varyantSelect.innerHTML = '<option value="">Bu ürün için varyant bulunamadı</option>';
                    showNotification(`"${itemName}" için varyant bulunamadı`, 'info');
                }
            } else {
                varyantSelect.innerHTML = '<option value="">Varyant yüklenemedi</option>';
                showNotification(`Varyant yükleme hatası: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error loading variants:', error);
            const varyantSelect = document.getElementById('varyant-select');
            varyantSelect.innerHTML = '<option value="">Hata oluştu</option>';
            showNotification('Varyant verileri yüklenirken hata oluştu', 'error');
        } finally {
            const varyantSelect = document.getElementById('varyant-select');
            varyantSelect.disabled = false;
        }
    }

    async function loadColors(itemName) {
        try {
            const rezinSelect = document.getElementById('rezin-select');

            // Loading state
            rezinSelect.innerHTML = '<option value="">Yükleniyor...</option>';
            rezinSelect.disabled = true;

            // API'den renkleri çek
            const response = await fetch(`http://localhost:5000/api/wms/colors?item=${encodeURIComponent(itemName)}`);
            const data = await response.json();

            if (data.success) {
                if (data.data && data.data.length > 0) {
                    populateDropdown('rezin-select', data.data, 'Renk seçiniz');
                    showNotification(`${data.count} renk yüklendi`, 'success');
                } else {
                    // Renk bulunamadı ama hata değil
                    rezinSelect.innerHTML = '<option value="">Bu ürün için renk bulunamadı</option>';
                    showNotification(`"${itemName}" için renk bulunamadı`, 'info');
                }
            } else {
                rezinSelect.innerHTML = '<option value="">Renk yüklenemedi</option>';
                showNotification(`Renk yükleme hatası: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error loading colors:', error);
            const rezinSelect = document.getElementById('rezin-select');
            rezinSelect.innerHTML = '<option value="">Hata oluştu</option>';
            showNotification('Renk verileri yüklenirken hata oluştu', 'error');
        } finally {
            const rezinSelect = document.getElementById('rezin-select');
            rezinSelect.disabled = false;
        }
    }

    function populateDropdown(selectId, items, placeholder = 'Seçiniz') {
        const select = document.getElementById(selectId);

        if (!select) {
            console.error(`Dropdown element not found: ${selectId}`);
            return;
        }

        // Mevcut seçenekleri temizle
        select.innerHTML = '';

        // Placeholder ekle
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.textContent = placeholder;
        select.appendChild(placeholderOption);

        // Verileri ekle
        items.forEach(item => {
            const option = document.createElement('option');
            option.value = item;
            option.textContent = item;
            select.appendChild(option);
        });

        console.log(`Populated ${selectId} with ${items.length} items`);
    }

    function setupAgramyInput() {
        const agramyInput = document.getElementById('agramy-input');

        if (agramyInput) {
            // Focus event - input alanına tıklandığında
            agramyInput.addEventListener('focus', async function() {
                const selectedComPort = document.getElementById('comport-select').value;

                if (!selectedComPort) {
                    showNotification('Önce COM Port seçiniz!', 'warning');
                    this.blur(); // Focus'u kaldır
                    return;
                }

                // Input alanını aktif hale getir
                this.readOnly = false;
                this.placeholder = 'Terazi verisi bekleniyor...';

                // Seçili COM port ile bağlantı aç
                await openComPortForWeight(selectedComPort);
            });

            // Blur event - input alanından çıkıldığında (COM port kapatma)
            agramyInput.addEventListener('blur', function() {
                // COM port'u kapatma, sadece readonly yap
                // Port GÖŞ butonuna basıldığında kapatılacak
                this.readOnly = true;
                this.placeholder = 'Ağırlık değeri...';
            });

            // Enter tuşuna basıldığında blur yap
            agramyInput.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    this.blur();
                }
            });
        }
    }

    // Ağramy için COM port yönetimi
    let agramyPortSettings = {
        isConnected: false,
        portName: '',
        dataReadingInterval: null
    };

    async function openComPortForWeight(portName) {
        try {
            showNotification(`${portName} terazi için açılıyor...`, 'info');

            const response = await fetch('http://localhost:5000/api/serial/open', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    port: portName,
                    baudRate: 9600,  // Terazi için standart ayarlar
                    dataBits: 8,
                    stopBits: 'One',
                    parity: 'None'
                })
            });

            const data = await response.json();

            if (data.success) {
                agramyPortSettings.isConnected = true;
                agramyPortSettings.portName = portName;

                // Real-time veri okumaya başla
                startWeightDataReading();

                // Terazi butonunu yeşil yap
                updateTeraziButtonStatus(true);

                showNotification(`${portName} terazi bağlantısı kuruldu`, 'success');
            } else {
                showNotification(`Terazi bağlantı hatası: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error opening COM port for weight:', error);
            showNotification('Terazi bağlantısı kurulamadı', 'error');
        }
    }

    async function closeComPortForWeight(portName) {
        try {
            // Veri okumayı durdur
            stopWeightDataReading();

            const response = await fetch('http://localhost:5000/api/serial/close', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    port: portName
                })
            });

            const data = await response.json();

            agramyPortSettings.isConnected = false;
            agramyPortSettings.portName = '';

            // Terazi butonunu gri yap
            updateTeraziButtonStatus(false);

            if (data.success) {
                showNotification(`${portName} terazi bağlantısı kapatıldı`, 'info');
            } else {
                showNotification(`Terazi kapatma hatası: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error closing COM port for weight:', error);
            agramyPortSettings.isConnected = false;
            agramyPortSettings.portName = '';

            // Terazi butonunu gri yap (hata durumunda da)
            updateTeraziButtonStatus(false);

            showNotification('Terazi bağlantısı kapatılamadı', 'error');
        }
    }

    function startWeightDataReading() {
        if (agramyPortSettings.dataReadingInterval) {
            clearInterval(agramyPortSettings.dataReadingInterval);
        }

        // Her 300ms'de veri oku (daha hızlı response için)
        agramyPortSettings.dataReadingInterval = setInterval(readWeightData, 300);
    }

    function stopWeightDataReading() {
        if (agramyPortSettings.dataReadingInterval) {
            clearInterval(agramyPortSettings.dataReadingInterval);
            agramyPortSettings.dataReadingInterval = null;
        }
    }

    async function readWeightData() {
        if (!agramyPortSettings.isConnected || !agramyPortSettings.portName) {
            stopWeightDataReading();
            return;
        }

        try {
            const response = await fetch(`http://localhost:5000/api/serial/read?port=${encodeURIComponent(agramyPortSettings.portName)}`);
            const data = await response.json();

            if (data.success && data.data && data.data.trim() !== '') {
                const rawData = data.data.trim();

                // C# örneğindeki gibi sayıyı extract et
                const extractedWeight = extractWeightFromData(rawData);

                if (extractedWeight) {
                    const agramyInput = document.getElementById('agramy-input');
                    if (agramyInput && document.activeElement === agramyInput) {
                        agramyInput.value = extractedWeight;
                    }
                }
            }
        } catch (error) {
            console.error('Error reading weight data:', error);
            // Sessizce devam et, her hata için notification gösterme
        }
    }

    function extractWeightFromData(rawData) {
        try {
            // C# örneğindeki regex pattern: (\d+(\.\d+)?)
            // Sayı pattern'ini ara (ondalık sayılar dahil)
            const match = rawData.match(/(\d+(?:\.\d+)?)/);

            if (match) {
                return match[1];
            }

            return null;
        } catch (error) {
            console.error('Error extracting weight:', error);
            return null;
        }
    }

    function updateTeraziButtonStatus(isConnected) {
        const teraziBtn = document.getElementById('teraziBtn');

        if (teraziBtn) {
            if (isConnected) {
                teraziBtn.classList.add('connected');
                teraziBtn.title = 'Terazi - Bağlı';
            } else {
                teraziBtn.classList.remove('connected');
                teraziBtn.title = 'Terazi';
            }
        }
    }

    // Paket Göç modal butonları
    const teraziBtn = document.getElementById('teraziBtn');
    const yazdirBtn = document.getElementById('yazdirBtn');
    const gosBtn = document.getElementById('gosBtn');

    if (teraziBtn) {
        teraziBtn.addEventListener('click', function() {
            openTeraziAyarlariModal();
        });
    }

    if (yazdirBtn) {
        yazdirBtn.addEventListener('click', function() {
            showNotification('Etiket yazdırılıyor...', 'info');
            // Yazdırma işlemleri burada yapılacak
        });
    }

    if (gosBtn) {
        gosBtn.addEventListener('click', function() {
            // Form verilerini topla
            const formData = collectPaketGocData();

            if (validatePaketGocData(formData)) {
                processPaketGoc(formData);
            }
        });
    }

    function collectPaketGocData() {
        const modal = document.getElementById('paketGocModal');
        const selects = modal.querySelectorAll('select');

        return {
            onum: selects[0].value,
            varyant: selects[1].value,
            rezinRenk: selects[2].value,
            agramy: selects[3].value,
            calisik: selects[4].value,
            comPort: selects[5].value
        };
    }

    function validatePaketGocData(data) {
        // Basit validasyon
        for (const [key, value] of Object.entries(data)) {
            if (!value) {
                showNotification(`Lütfen ${key} alanını seçiniz!`, 'error');
                return false;
            }
        }
        return true;
    }

    async function processPaketGoc(data) {
        showNotification('Paket göç işlemi başlatılıyor...', 'info');

        // Ağramy için açık olan COM port'u kapat
        if (agramyPortSettings.isConnected && agramyPortSettings.portName) {
            await closeComPortForWeight(agramyPortSettings.portName);
        }

        // Burada gerçek işlem yapılacak
        setTimeout(() => {
            showNotification('Paket göç işlemi tamamlandı!', 'success');
            paketGocModal.style.display = 'none';
        }, 2000);
    }

    // Terazi Ayarları Modal functionality
    const teraziAyarlariModal = document.getElementById('teraziAyarlariModal');
    let teraziSettings = {
        baudRate: '9600',
        dataBits: '8',
        stopBits: 'One',
        parityBits: 'None',
        portName: 'COM3',
        isConnected: false
    };

    // Load terazi settings from localStorage
    const savedTeraziSettings = localStorage.getItem('teraziSettings');
    if (savedTeraziSettings) {
        teraziSettings = { ...teraziSettings, ...JSON.parse(savedTeraziSettings) };
    }

    async function openTeraziAyarlariModal() {
        // Populate form with current settings
        document.getElementById('baudRate').value = teraziSettings.baudRate;
        document.getElementById('dataBits').value = teraziSettings.dataBits;
        document.getElementById('stopBits').value = teraziSettings.stopBits;
        document.getElementById('parityBits').value = teraziSettings.parityBits;

        // Load available COM ports
        await loadComPortsForTerazi();

        // Update port status
        updatePortStatus();

        // Clear receiver data
        document.getElementById('receiverData').value = '';
        document.getElementById('dataLength').textContent = '00';

        teraziAyarlariModal.style.display = 'flex';
    }

    async function loadComPortsForTerazi() {
        try {
            const portSelect = document.getElementById('portName');
            portSelect.innerHTML = '<option value="">Yükleniyor...</option>';

            const response = await fetch('http://localhost:5000/api/system/com-ports');
            const data = await response.json();

            if (data.success) {
                portSelect.innerHTML = '<option value="">COM Port seçiniz</option>';

                data.data.forEach(port => {
                    const option = document.createElement('option');
                    option.value = port;
                    option.textContent = port;
                    if (port === teraziSettings.portName) {
                        option.selected = true;
                    }
                    portSelect.appendChild(option);
                });

                console.log('COM Ports loaded for terazi:', data.detailed_data);
            } else {
                portSelect.innerHTML = '<option value="">Port yüklenemedi</option>';
            }
        } catch (error) {
            console.error('Error loading COM ports for terazi:', error);
            const portSelect = document.getElementById('portName');
            portSelect.innerHTML = '<option value="">Hata oluştu</option>';
        }
    }

    function updatePortStatus() {
        const statusText = document.getElementById('portStatusText');
        if (teraziSettings.isConnected) {
            statusText.textContent = 'ON';
            statusText.classList.add('connected');
        } else {
            statusText.textContent = 'OFF';
            statusText.classList.remove('connected');
        }
    }

    // Port control buttons
    const openPortBtn = document.getElementById('openPortBtn');
    const closePortBtn = document.getElementById('closePortBtn');
    const clearDataBtn = document.getElementById('clearDataBtn');

    if (openPortBtn) {
        openPortBtn.addEventListener('click', function() {
            openSerialPort();
        });
    }

    if (closePortBtn) {
        closePortBtn.addEventListener('click', function() {
            closeSerialPort();
        });
    }

    if (clearDataBtn) {
        clearDataBtn.addEventListener('click', function() {
            clearReceiverData();
        });
    }

    async function openSerialPort() {
        const portName = document.getElementById('portName').value;
        const baudRate = document.getElementById('baudRate').value;
        const dataBits = document.getElementById('dataBits').value;
        const stopBits = document.getElementById('stopBits').value;
        const parityBits = document.getElementById('parityBits').value;

        if (!portName) {
            showNotification('Lütfen COM port seçin!', 'error');
            return;
        }

        try {
            showNotification(`${portName} portu açılıyor...`, 'info');

            const response = await fetch('http://localhost:5000/api/serial/open', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    port: portName,
                    baudRate: parseInt(baudRate),
                    dataBits: parseInt(dataBits),
                    stopBits: stopBits,
                    parity: parityBits
                })
            });

            const data = await response.json();

            if (data.success) {
                teraziSettings.isConnected = true;
                teraziSettings.portName = portName;
                updatePortStatus();
                showNotification(data.message, 'success');

                // Start real-time data reading
                startDataReading();
            } else {
                showNotification(`Port açma hatası: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error opening serial port:', error);
            showNotification('Port açma işleminde hata oluştu', 'error');
        }
    }

    async function closeSerialPort() {
        const portName = teraziSettings.portName;

        if (!teraziSettings.isConnected || !portName) {
            showNotification('Port zaten kapalı!', 'warning');
            return;
        }

        try {
            const response = await fetch('http://localhost:5000/api/serial/close', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    port: portName
                })
            });

            const data = await response.json();

            teraziSettings.isConnected = false;
            updatePortStatus();
            stopDataReading();

            if (data.success) {
                showNotification(data.message, 'info');
            } else {
                showNotification(`Port kapama hatası: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error closing serial port:', error);
            teraziSettings.isConnected = false;
            updatePortStatus();
            stopDataReading();
            showNotification('Port kapama işleminde hata oluştu', 'error');
        }
    }

    function clearReceiverData() {
        document.getElementById('receiverData').value = '';
        document.getElementById('dataLength').textContent = '00';
        showNotification('Veri temizlendi!', 'info');
    }

    // Real-time data reading variables
    let dataReadingInterval = null;

    function startDataReading() {
        if (dataReadingInterval) {
            clearInterval(dataReadingInterval);
        }

        // Read data every 500ms for real-time updates
        dataReadingInterval = setInterval(readSerialData, 500);
    }

    function stopDataReading() {
        if (dataReadingInterval) {
            clearInterval(dataReadingInterval);
            dataReadingInterval = null;
        }
    }

    async function readSerialData() {
        if (!teraziSettings.isConnected || !teraziSettings.portName) {
            stopDataReading();
            return;
        }

        try {
            const response = await fetch(`http://localhost:5000/api/serial/read?port=${encodeURIComponent(teraziSettings.portName)}`);
            const data = await response.json();

            if (data.success && data.data && data.data.trim() !== '') {
                const receiverData = document.getElementById('receiverData');
                const dataLength = document.getElementById('dataLength');
                const addToOldData = document.getElementById('addToOldData').checked;
                const alwaysUpdate = document.getElementById('alwaysUpdate').checked;

                const newData = data.data.trim() + '\n';
                const timestamp = new Date().toLocaleTimeString();
                const formattedData = `[${timestamp}] ${newData}`;

                if (addToOldData) {
                    receiverData.value += formattedData;
                } else {
                    receiverData.value = formattedData;
                }

                // Update data length
                dataLength.textContent = receiverData.value.length.toString().padStart(2, '0');

                // Auto scroll to bottom
                receiverData.scrollTop = receiverData.scrollHeight;

                // Show notification for new data if always update is enabled
                if (alwaysUpdate) {
                    showNotification(`Yeni veri: ${data.data}`, 'info');
                }
            }
        } catch (error) {
            console.error('Error reading serial data:', error);
            // Don't show error notifications for every failed read to avoid spam
        }
    }

    // Save terazi settings
    const saveTeraziBtn = document.getElementById('saveTeraziBtn');
    const cancelTeraziBtn = document.getElementById('cancelTeraziBtn');

    if (saveTeraziBtn) {
        saveTeraziBtn.addEventListener('click', function() {
            saveTeraziSettings();
        });
    }

    if (cancelTeraziBtn) {
        cancelTeraziBtn.addEventListener('click', function() {
            stopDataReading();
            teraziAyarlariModal.style.display = 'none';
        });
    }

    function saveTeraziSettings() {
        // Update settings from form
        teraziSettings.baudRate = document.getElementById('baudRate').value;
        teraziSettings.dataBits = document.getElementById('dataBits').value;
        teraziSettings.stopBits = document.getElementById('stopBits').value;
        teraziSettings.parityBits = document.getElementById('parityBits').value;
        teraziSettings.portName = document.getElementById('portName').value;

        // Save to localStorage
        localStorage.setItem('teraziSettings', JSON.stringify(teraziSettings));

        // Close modal
        teraziAyarlariModal.style.display = 'none';

        showNotification('Terazi ayarları kaydedildi!', 'success');
    }

    // Notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }
});
