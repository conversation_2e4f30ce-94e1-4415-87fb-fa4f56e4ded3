using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.Linq.Mapping;
using System.Data.SqlClient;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Printing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using ZXing;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TextBox;

namespace WindowsFormsApp5
{
    public partial class frmKarobka : Form
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
        string dataIN;
        public frmKarobka()
        {
            InitializeComponent();
        }
        public int rowCount = 1;
        string TEST;
        private void frmKarobka_Load(object sender, EventArgs e)
        {
            //btnOpen.PerformClick();
            frmLogin frmLogin = new frmLogin();
            lblUser.Text = frmLogin.CurrentUsername;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                // SQL sorgusunu hazırla ve çalıştır
                string sqlQueryK = "SELECT USERNR FROM BB_WMS_USERLIST WHERE USERNAME = @ParamName";
                using (SqlCommand sqlCommand = new SqlCommand(sqlQueryK, connection))
                {
                    // Eğer parametreler kullanıyorsanız, onları burada ekleyebilirsiniz.
                    sqlCommand.Parameters.AddWithValue("@ParamName", lblUser.Text.ToString());

                    // Sorguyu çalıştır ve sonucu al
                    object result = sqlCommand.ExecuteScalar();

                    // Sonucu TEST değişkenine ata (varsayılan değeri null olacaktır)
                    TEST = result != DBNull.Value ? result.ToString() : null;
                }
                connection.Close();
            }
            // DataGridView'a POZ butonunu ekleme ve rengini değiştirme



            string sqlQuery = "SELECT ITEM FROM BB_WMS_DISPLINE";
            //string sqlQuery2 = "SELECT ITEMREF FROM BB_WMS_BOX";
            try
            {
                // Veritabanı bağlantısını oluştur
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    // Komut ve bağlantıyı ilişkilendir
                    using (SqlCommand command = new SqlCommand(sqlQuery, connection))
                    {
                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // ComboBox içeriğini temizle
                        comboBox1.Items.Clear();

                        // Veriyi oku ve ComboBox'a ekle
                        while (reader.Read())
                        {
                            comboBox1.Items.Add(reader["ITEM"].ToString());

                        }

                        // Okuyucuyu kapat
                        reader.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Veritabanına bağlanırken hata oluştu: " + ex.Message);
            }
            //comboBox1.SelectedIndex = 0;
            string smen = "SELECT LOGICALREF FROM BB_WMS_SHIFTLIST";
            //string sqlQuery2 = "SELECT ITEMREF FROM BB_WMS_BOX";
            try
            {
                // Veritabanı bağlantısını oluştur
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    // Komut ve bağlantıyı ilişkilendir
                    using (SqlCommand command = new SqlCommand(smen, connection))
                    {
                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // ComboBox içeriğini temizle
                        comboBox4.Items.Clear();

                        // Veriyi oku ve ComboBox'a ekle
                        while (reader.Read())
                        {
                            comboBox4.Items.Add(reader["LOGICALREF"].ToString());

                        }

                        // Okuyucuyu kapat
                        reader.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Veritabanına bağlanırken hata oluştu: " + ex.Message);
            }

            /////////
            //richTextBox1.MaxLength = 10;
            string[] ports = SerialPort.GetPortNames();
            cBoxCOMPORT.Items.AddRange(ports);
            //cBoxCOMPORT.SelectedIndex = 0;

            btnOpen.Enabled = true;
            btnClose.Enabled = false;
            chBoxAddToOldData.Checked = true;
            chBoxAlwaysUpdate.Checked = false;
            //richTextBox1.MaxLength = 17;
            ////////////



        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            comboBox2.Text = "";
            comboBox3.Text = "";
            lblVR.Text = "";


            try
            {
                string selectedItem = comboBox1.SelectedItem.ToString();

                // Veritabanı bağlantısını oluştur
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    // SQL sorgusu
                    string sqlQuery = "SELECT ITEMREF FROM BB_WMS_DISPLINE WHERE ITEM = @SelectedItem";
                    //string sqlQuery2 = "SELECT VARIANTREF FROM BB_WMS_DISPLINE WHERE ITEM = @SelectedItem";
                    string sqlQuery3 = "SELECT PRODORDREF FROM BB_WMS_DISPLINE WHERE ITEM = @SelectedItem";
                    string sqlQuery4 = "SELECT WSREF FROM BB_WMS_DISPLINE WHERE ITEM = @SelectedItem";
                    string sqlQuery5 = "SELECT DISPLINEREF FROM BB_WMS_DISPLINE WHERE ITEM = @SelectedItem";
                    string sqlQuery6 = "SELECT PLNAMOUNT FROM BB_WMS_DISPLINE WHERE ITEM = @SelectedItem";
                    string sqlQuery7 = "SELECT ACTAMOUNT FROM BB_WMS_DISPLINE WHERE ITEM = @SelectedItem";

                    //ITEMREF
                    using (SqlCommand command = new SqlCommand(sqlQuery, connection))
                    {
                        // Parametre ekleyerek SQL sorgusunu güvenli hale getir
                        command.Parameters.AddWithValue("@SelectedItem", selectedItem);

                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // Okuyucu veriyi okudu mu kontrol et
                        if (reader.Read())
                        {
                            // Okunan değeri lblKarobka'nın Text özelliğine at
                            lblItemref.Text = reader["ITEMREF"].ToString();
                            TTT = Convert.ToInt32(lblItemref.Text);
                        }

                        // Okuyucuyu kapat
                        reader.Close();
                        connection.Close();
                    }
                    using (SqlCommand command = new SqlCommand(sqlQuery3, connection))
                    {
                        // Parametre ekleyerek SQL sorgusunu güvenli hale getir
                        command.Parameters.AddWithValue("@SelectedItem", selectedItem);

                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // Okuyucu veriyi okudu mu kontrol et
                        if (reader.Read())
                        {
                            // Okunan değeri lblKarobka'nın Text özelliğine at
                            lblPR.Text = reader["PRODORDREF"].ToString();
                        }

                        // Okuyucuyu kapat
                        reader.Close();
                        connection.Close();
                    }
                    //WSREF
                    using (SqlCommand command = new SqlCommand(sqlQuery4, connection))
                    {
                        // Parametre ekleyerek SQL sorgusunu güvenli hale getir
                        command.Parameters.AddWithValue("@SelectedItem", selectedItem);

                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // Okuyucu veriyi okudu mu kontrol et
                        if (reader.Read())
                        {
                            // Okunan değeri lblKarobka'nın Text özelliğine at
                            lblWR.Text = reader["WSREF"].ToString();
                        }

                        // Okuyucuyu kapat
                        reader.Close();
                        connection.Close();
                    }
                    //DPLREF
                    using (SqlCommand command = new SqlCommand(sqlQuery5, connection))
                    {
                        // Parametre ekleyerek SQL sorgusunu güvenli hale getir
                        command.Parameters.AddWithValue("@SelectedItem", selectedItem);

                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // Okuyucu veriyi okudu mu kontrol et
                        if (reader.Read())
                        {
                            // Okunan değeri lblKarobka'nın Text özelliğine at
                            lblDR.Text = reader["DISPLINEREF"].ToString();
                        }

                        // Okuyucuyu kapat
                        reader.Close();
                        connection.Close();
                    }

                    //PLNAMOUNT
                    using (SqlCommand command = new SqlCommand(sqlQuery6, connection))
                    {
                        // Parametre ekleyerek SQL sorgusunu güvenli hale getir
                        command.Parameters.AddWithValue("@SelectedItem", selectedItem);

                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // Okuyucu veriyi okudu mu kontrol et
                        if (reader.Read())
                        {
                            // Okunan değeri lblKarobka'nın Text özelliğine at
                            lblPlan.Text = reader["PLNAMOUNT"].ToString();
                        }

                        // Okuyucuyu kapat
                        reader.Close();
                        connection.Close();
                    }
                    //ACTAMOUNT
                    using (SqlCommand command = new SqlCommand(sqlQuery7, connection))
                    {
                        // Parametre ekleyerek SQL sorgusunu güvenli hale getir
                        command.Parameters.AddWithValue("@SelectedItem", selectedItem);

                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // Okuyucu veriyi okudu mu kontrol et
                        if (reader.Read())
                        {
                            // Okunan değeri lblKarobka'nın Text özelliğine at
                            lblTayyn.Text = reader["ACTAMOUNT"].ToString();
                        }

                        // Okuyucuyu kapat
                        reader.Close();
                        connection.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Hata oluştu: " + ex.Message);
            }
            int plan = Convert.ToInt32(lblPlan.Text);
            int tayyn = Convert.ToInt32(lblTayyn.Text);
            int galdy = plan - tayyn;
            lblGalan.Text = galdy.ToString();

            ///vrt
            string sqlVRT = "SELECT VRTNAME FROM BB_WMS_DISPLINE where ITEMREF = " + lblItemref.Text + "";

            try
            {
                // Veritabanı bağlantısını oluştur
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    // Komut ve bağlantıyı ilişkilendir
                    using (SqlCommand command = new SqlCommand(sqlVRT, connection))
                    {
                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // ComboBox içeriğini temizle
                        comboBox2.Items.Clear();

                        // Veriyi oku ve ComboBox'a ekle
                        while (reader.Read())
                        {
                            comboBox2.Items.Add(reader["VRTNAME"].ToString());

                        }

                        // Okuyucuyu kapat
                        reader.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Veritabanına bağlanırken hata oluştu: " + ex.Message);
            }
            //comboBox2.SelectedIndex = 0;
            ///
            //gasket color
            string sqlGasket = "SELECT GASKET_COLOR FROM BB_WMS_DISPLINE where ITEMREF = " + lblItemref.Text + "";

            try
            {
                // Veritabanı bağlantısını oluştur
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    // Komut ve bağlantıyı ilişkilendir
                    using (SqlCommand command = new SqlCommand(sqlGasket, connection))
                    {
                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // ComboBox içeriğini temizle
                        comboBox3.Items.Clear();

                        // Veriyi oku ve ComboBox'a ekle
                        while (reader.Read())
                        {
                            comboBox3.Items.Add(reader["GASKET_COLOR"].ToString());

                        }

                        // Okuyucuyu kapat
                        reader.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Veritabanına bağlanırken hata oluştu: " + ex.Message);
            }
            //  comboBox3.SelectedIndex = 0;
            ///
            ///
        }

        private void btnYatdaSakla_Click(object sender, EventArgs e)
        {
            if (label9.Text == "COM PORT")//comboBox1.Text != "" && comboBox2.Text != "" && comboBox4.Text != "") //&& comboBox3.Text != "" && txtAgram.Text != ""
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    //using (SqlCommand command = new SqlCommand("BB_WMS_PACKLINE_INS", connection))
                    //{
                    //    command.CommandType = CommandType.StoredProcedure;

                    //    SqlParameter outputParameter = new SqlParameter
                    //    {
                    //        ParameterName = "@PACKNO1",
                    //        SqlDbType = SqlDbType.VarChar,
                    //        Size = 50,
                    //        Direction = ParameterDirection.Output
                    //    };


                    //    try
                    //    {
                    //        // Stored procedure parametrelerini ekleyin
                    //        command.Parameters.AddWithValue("@USER", TEST);
                    //        command.Parameters.AddWithValue("@ITEMREF", Convert.ToInt32(lblItemref.Text));
                    //        command.Parameters.AddWithValue("@VARIANTREF", Convert.ToInt32(lblVR.Text));
                    //        command.Parameters.AddWithValue("@PRODORDREF", Convert.ToInt32(lblPR.Text));
                    //        command.Parameters.AddWithValue("@WSREF", Convert.ToInt32(lblWR.Text));
                    //       command.Parameters.AddWithValue("@WEIGHT", Convert.ToString(txtAgram.Text));
                    //        command.Parameters.AddWithValue("@DPLREF", Convert.ToInt32(lblDR.Text));
                    //        command.Parameters.AddWithValue("@SHIFTREF", Convert.ToInt32(comboBox4.SelectedItem));
                    //        //SqlParameter parameter = command.Parameters.AddWithValue("@BOXTABLE", dataTable);
                    //        //parameter.SqlDbType = SqlDbType.Structured;
                    //        //parameter.TypeName = "dbo.BOXLINE"; // Tablo tipi adı


                    //        //int rowsAffected = command.ExecuteNonQuery();


                    //        command.Parameters.Add(outputParameter); // Output parametresini ekleyin

                    //        // ExecuteNonQuery() kullanarak stored procedure'ı çalıştırın
                    //        ////////

                    //        int rowsAffected = command.ExecuteNonQuery();
                    //    }
                    //    catch (Exception ex)
                    //    {

                    //        MessageBox.Show(ex.Message);
                    //    }


                    // Output parametresinin değerini alın


                    // Raporu görüntüleyin
                    //ReportPrintTool printTool = new ReportPrintTool(report);
                    // printTool.Print(); // Yazıcıya gönder
                    // printTool.ShowRibbonPreview();

                    //report.CreateDocument();

                    //// PDF dosyasının kaydedileceği yol
                    //string pdfPath = @"C:\Temp\report.pdf";

                    //// PDF olarak dışa aktar
                    //report.ExportToPdf(pdfPath);

                    //string PACKNO = "444444444444555555555555";// outputParameter.Value.ToString();
                    //                                           // MessageBox.Show(BOXNO1.ToString());
                    //                                           //BarcodeWriter writer = new BarcodeWriter() { Format=BarcodeFormat.CODE_128 };
                    //                                           //pictureBox1.Image=writer.Write( BOXNO1 );
                    //XtraReport1 report = new XtraReport1();
                    //report.LoadLayout("XtraReport1.repx");
                    //report.Parameters["parameter1"].Value = PACKNO;
                    //string imagePath = @"C:\Temp\report.bmp";




                    string PACKNO = textBox1.Text;
                    XtraReport1 report = new XtraReport1();
                    report.LoadLayout("XtraReport1.repx");

                    // Sayfa genişliğini artır
                    report.PageWidth = 1000; // Genişliği artır (deneyerek uygun değeri bulabilirsin)
                    report.Margins = new System.Drawing.Printing.Margins(10, 0, 0, 0);

                    // Parametreyi ata
                    report.Parameters["parameter1"].Value = PACKNO;

                    // Çıktı dosya yolu
                    string imagePath = @"C:\Temp\report.bmp";

                    using (PrintingSystem ps = new PrintingSystem())
                    {
                        report.CreateDocument();
                        ImageExportOptions options = new ImageExportOptions
                        {
                            Format = ImageFormat.Bmp,  // PNG formatında kaydet
                            Resolution = 300,
                            ExportMode = ImageExportMode.SingleFile // Sayfanın tamamını almasını sağlar
                        };
                        report.ExportToImage(imagePath, options);
                    }



                    //// RFID
                    if (!File.Exists(imagePath))
                    {
                        MessageBox.Show("Belirtilen resim bulunamadı!");
                        return;
                    }

                    string zplImage = ConvertImageToZPL(imagePath);
                    string zplCommand = "^XA\n" +
                                        "^LL560\n" + // Etiket uzunluğu: 70mm ≈ 560 dots
                                        "^PW800\n" + // Etiket genişliği: 100mm ≈ 800 dots
                                        "!RFID CALIBRATE\n" +
                                        "^RS8,,,2^FS\n" +
                                         "^RFW,H,2,6,1^FD" + PACKNO + "^FS\n" + // EPC Bank'a Yazma
                                         zplImage + "\n" +
                                        "^XZ\n";

                    try
                    {
                        SendZplToNetworkPrinter("192.168.3.193", 9100, zplCommand);
                        MessageBox.Show("RFID verisi ve resim başarıyla yazıldı!");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Yazdırma hatası: " + ex.Message);
                    }
                    ////

                    PACKNO = "";

                    connection.Close();
                }

            }
            else
            {
                MessageBox.Show("Ähli zerur maglumatlary giriziň!");
            }

        }
        /// <rfid2>
        private string ConvertImageToZPL(string imagePath)
        {
            using (Bitmap originalBmp = new Bitmap(imagePath))
            {
                int labelWidth = 800; // Etiket genişliği: 100mm * 8 dots/mm
                int labelHeight = 560; // Etiket uzunluğu: 70mm * 8 dots/mm

                // Resmi etiket boyutuna sığdırın
                using (Bitmap resizedBmp = new Bitmap(originalBmp, labelWidth, labelHeight))
                {
                    int widthBytes = (labelWidth + 7) / 8;
                    int dataSize = widthBytes * labelHeight;
                    StringBuilder sb = new StringBuilder();
                    sb.AppendFormat("^FO0,0^GFA,{0},{1},{2},", dataSize, dataSize, widthBytes);

                    for (int y = 0; y < labelHeight; y++)
                    {
                        for (int x = 0; x < labelWidth; x += 8)
                        {
                            int byteValue = 0;
                            for (int bit = 0; bit < 8; bit++)
                            {
                                if (x + bit < labelWidth)
                                {
                                    Color pixel = resizedBmp.GetPixel(x + bit, y);
                                    int grayscale = (pixel.R + pixel.G + pixel.B) / 3;
                                    if (grayscale < 130) // Siyah piksel //128 dine siyah
                                    {
                                        byteValue |= (1 << (7 - bit));
                                    }
                                }
                            }
                            sb.Append(byteValue.ToString("X2"));
                        }
                    }
                    return sb.ToString();
                }
            }
        }

        private void SendZplToNetworkPrinter(string printerIp, int port, string zplCommand)
        {
            using (TcpClient client = new TcpClient(printerIp, port))
            using (NetworkStream stream = client.GetStream())
            using (StreamWriter writer = new StreamWriter(stream, Encoding.ASCII))
            {
                writer.Write(zplCommand);
                writer.Flush();
            }
        }
        /// </rfid2>

        public int TTT;


        private void simpleButton1_Click(object sender, EventArgs e)
        {
            frmReportKarobka pp = new frmReportKarobka();
            pp.ShowDialog();
        }

        private void panel1_Paint(object sender, PaintEventArgs e)
        {

        }

        private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                string selectedItem = comboBox2.SelectedItem.ToString();

                // Veritabanı bağlantısını oluştur
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    // SQL sorgusu

                    string sqlQuery2 = "SELECT VARIANTREF FROM BB_WMS_DISPLINE WHERE VRTNAME = @SelectedItem";



                    //VARIANTREF
                    using (SqlCommand command = new SqlCommand(sqlQuery2, connection))
                    {
                        // Parametre ekleyerek SQL sorgusunu güvenli hale getir
                        command.Parameters.AddWithValue("@SelectedItem", selectedItem);

                        // Bağlantıyı aç
                        connection.Open();

                        // Veri okuma işlemi için bir okuyucu oluştur
                        SqlDataReader reader = command.ExecuteReader();

                        // Okuyucu veriyi okudu mu kontrol et
                        if (reader.Read())
                        {
                            // Okunan değeri lblKarobka'nın Text özelliğine at
                            lblVR.Text = reader["VARIANTREF"].ToString();
                        }

                        // Okuyucuyu kapat
                        reader.Close();
                        connection.Close();
                    }

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Hata oluştu: " + ex.Message);
            }



        }

        private void comboBox3_SelectedIndexChanged(object sender, EventArgs e)
        {

        }



        private void btnOpen_Click(object sender, EventArgs e)
        {
            try
            {
                serialPort1.PortName = cBoxCOMPORT.Text;
                serialPort1.BaudRate = Convert.ToInt32(cBoxBaudRate.Text);
                serialPort1.DataBits = Convert.ToInt32(cBoxDataBits.Text);
                serialPort1.StopBits = (StopBits)Enum.Parse(typeof(StopBits), cBoxStopBits.Text);
                serialPort1.Parity = (Parity)Enum.Parse(typeof(Parity), cBoxParityBits.Text);

                serialPort1.Open();
                progressBar1.Value = 100;
                btnOpen.Enabled = false;
                btnClose.Enabled = true;
                lblComStatus.Text = "ON";
            }
            catch (Exception err)
            {

                MessageBox.Show(err.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                btnOpen.Enabled = true;
                btnClose.Enabled = false;
                lblComStatus.Text = "OFF";
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            // timer1.Stop();
            if (serialPort1.IsOpen)
            {
                serialPort1.Close();
                progressBar1.Value = 0;
                btnOpen.Enabled = true;
                btnClose.Enabled = false;
                lblComStatus.Text = "OFF";
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            richTextBox1.Text = "";
        }

        private void serialPort1_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            //timer1.Start();
            dataIN = serialPort1.ReadExisting();
            this.Invoke(new EventHandler(ShowData));

        }

        private void ShowData(object sender, EventArgs e)
        {
            int dataINLength = dataIN.Length;
            lblDataINLength.Text = string.Format("{0:00}", dataINLength);
            if (chBoxAlwaysUpdate.Checked)
            {
                timer1.Start();

            }
            else if (chBoxAddToOldData.Checked)
            {
                richTextBox1.Text += dataIN;
                //txtAgram.Text += dataIN;
                ///
                string textBoxContent = richTextBox1.Text;
                try
                {
                    // İlk 10 karakterlik parçayı al
                    if (textBoxContent.Length >= 20)
                    {
                        string firstPart = textBoxContent.Substring(0, 20);
                        txtAgram.Text = ExtractNumber(firstPart);
                    }
                    else
                    {
                        txtAgram.Text = ExtractNumber(textBoxContent);
                    }

                    //MessageBox.Show(agram, "Extracted Number");
                }
                catch (Exception)
                {

                    MessageBox.Show("Täzeden basyň!");
                }
                ///

                if (richTextBox1.Text.Length >= 250)
                {
                    // Uzunluk 16 veya daha fazla ise TextBox'u temizleyin
                    txtAgram.Clear();
                    richTextBox1.Clear();
                }
            }

        }

        private void chBoxAlwaysUpdate_CheckedChanged(object sender, EventArgs e)
        {
            if (chBoxAlwaysUpdate.Checked)
            {
                chBoxAlwaysUpdate.Checked = true;
                chBoxAddToOldData.Checked = false;
            }
            else
            {
                chBoxAddToOldData.Checked = true;
            }
        }

        private void chBoxAddToOldData_CheckedChanged(object sender, EventArgs e)
        {
            if (chBoxAddToOldData.Checked)
            {
                chBoxAlwaysUpdate.Checked = false;
                chBoxAddToOldData.Checked = true;
            }
            else
            {
                chBoxAlwaysUpdate.Checked = true;
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            richTextBox1.Text = dataIN;
            txtAgram.Text = dataIN;
        }



        private void simpleButton2_Click(object sender, EventArgs e)
        {
            if (this.Height != 677)
            {
                this.Height = 677;
            }
            else
            {
                this.Height = 305;
            }


        }

        private void richTextBox1_TextChanged(object sender, EventArgs e)
        {
            int dataOUTLength = richTextBox1.TextLength;
            //lblDataINLength.Text = string.Format("{0:00}", dataOUTLength);
            if (richTextBox1.TextLength == 1)
            {
                richTextBox1.Text = "";
            }
        }

        private void serialPort1_DataReceived_1(object sender, SerialDataReceivedEventArgs e)
        {
            //timer1.Start();
            dataIN = serialPort1.ReadExisting();
            this.Invoke(new EventHandler(ShowData));
        }

        private void txtAgram_EditValueChanged(object sender, EventArgs e)
        {
            if (txtAgram.Text.Length == 16)
            {
                txtAgram.Clear();
            }

        }

        private void btnsave_Click(object sender, EventArgs e)
        {
            string textBoxContent = txtAgram.Text;
            string agram = "";
            try
            {
                // İlk 10 karakterlik parçayı al
                if (textBoxContent.Length >= 20)
                {
                    string firstPart = textBoxContent.Substring(0, 20);
                    agram = ExtractNumber(firstPart);
                }
                else
                {
                    agram = ExtractNumber(textBoxContent);
                }

                MessageBox.Show(agram, "Extracted Number");
            }
            catch (Exception)
            {

                MessageBox.Show("Täzeden basyň!");
            }

        }
        private string ExtractNumber(string text)
        {
            // ",kg"den önceki sayıyı al
            Match match = Regex.Match(text, @"(\d+(\.\d+)?)");
            if (match.Success)
            {
                return match.Value;
            }
            return "";
        }

        private void txtAgram_Enter(object sender, EventArgs e)
        {
            btnOpen.PerformClick();
        }

        private void txtAgram_Leave(object sender, EventArgs e)
        {
            btnClose.PerformClick();
        }

        private void txtAgram_TextChanged(object sender, EventArgs e)
        {

        }
    }
}
